# The manifest for the "assessment" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: assessment
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  # Requests to this path will be forwarded to your service.
  # To match all requests you can use the "/" path.
  path: '/'
  # You can specify a custom health check path. The default is "/".
  healthcheck:
    path: '/api/healthcheck'
    success_codes: '200-399'
    healthy_threshold: 5
    unhealthy_threshold: 3
    interval: 30s
    timeout: 10s
    grace_period: 180s


# Optional fields for more advanced use-cases.
#
# variables:                    # Pass environment variables as key value pairs.
#   DB_NAME: postgres
#   DB_HOST: urecruits-dev-assessment-assessmentclusterdbclust-104m2x7sx6xrq.cluster-crbdnvdwwcn1.us-east-1.rds.amazonaws.com
#   DB_USERNAME: postgres
#   POSTGRES_PORT: 5432
#   REDIS_URL: redis://urecruits-dev-redis.6bseuu.0001.use1.cache.amazonaws.com:6379
#   EMAIL_SENDER: <EMAIL>
#   JUDGE0_URL: https://terminal.wtt-dev.urecruits.com/


#  LOG_LEVEL: info

# secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#   SENDGRID_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/SENDGRID_TOKEN
#   DB_PASS: "'arn:aws:secretsmanager:us-east-1:459226637808:secret:assessmentclusterAuroraSecr-KhNqwsIZ9KJ7-tCxS8W:password::'"

# You can override any of the values defined above by environment.
#environments:
#  test:
#    count: 2               # Number of tasks to run for the "test" environment.


# You can override any of the values defined above by environment.
environments:
  dev:
    image:
      build:
        dockerfile: dockerfiles/Dockerfile.assessment
        context: .
      port: 3000
    http:
      alias: 'wtt-dev.urecruits.com'
    cpu: 256
    memory: 512
    count: 1
    exec: true
    secrets:
      SENDGRID_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/SENDGRID_TOKEN
      DB_PASS:
        secretsmanager: 'urecruits-dev-assessment-AuroraPostgres:password::'
      DB_HOST:
        secretsmanager: 'urecruits-dev-assessment-AuroraPostgres:host::'
      DB1_PASS:
        secretsmanager: 'urecruits-dev-assessment-RDSMySQL:password::'
      DB1_HOST:
        secretsmanager: 'urecruits-dev-assessment-RDSMySQL:host::'
#      DB_PASS: "'arn:aws:secretsmanager:us-east-1:459226637808:secret:assessmentclusterAuroraSecr-KhNqwsIZ9KJ7-tCxS8W:password::'"
#      DB_HOST: "'arn:aws:secretsmanager:us-east-1:459226637808:secret:assessmentclusterAuroraSecr-KhNqwsIZ9KJ7-tCxS8W:host::'"
      JUDGE0_AUTH_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/JUDGE0_AUTH_TOKEN
      CONVERGENCE_PRIVATE_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/CONVERGENCE_PRIVATE_KEY
      HELLOSIGN_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_KEY
      HELLOSIGN_CLIENT_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_CLIENT_ID
      # OPEN_AI_SECRET_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/OPEN_AI_SECRET_KEY
      # TEMPORAL_SERVER: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TEMPORAL_SERVER
    variables:
      DB_NAME: postgres
      DB_USERNAME: postgres
      POSTGRES_PORT: 5432
      REDIS_URL: redis://urecruits-dev-redis.6bseuu.0001.use1.cache.amazonaws.com:6379
      EMAIL_SENDER: <EMAIL>
      FRONT_URL: https://urecruits.com
      JUDGE0_URL: https://judge0-ce.p.rapidapi.com
      JUDGE0_HOST: judge0-ce.p.rapidapi.com
      AUTH0_AUDIENCE: https://api-authz.urecruits.com/
      AUTH0_DOMAIN: https://auth.urecruits.com
      AUTH0_CLIENT_SECRET_MTM: ****************************************************************
      AUTH0_CLIENT_ID_MTM: JvrRE8piU5dTKIzwFgcWpK0TRuMnqUEZ
      AUTH0_DOMAIN_MTM: https://dev-9zt22me9.us.auth0.com
      MICROSERVICES_PRA_URL: https://recruitment-micro.urecruits.com
      HELLOSIGN_TEST_MODE: 1
      TEMPORAL_SERVER: recruitment-prod.lrnb6.tmprl.cloud:7233
      TEMPORAL_NAMESPACE: recruitment-prod.lrnb6
      ASSESSMENT_API_URI : https://wtt-dev.urecruits.com
      WEB_APP_URI : https://app.urecruits.com
      RECRUITMENT_API_URI : https://recruitment-micro.urecruits.com
      OPEN_AI_MODEL: gpt-4o
      OPEN_AI_SECRET_KEY: ***************************************************
      CLIENT_CA_PATH: /app/certs/client.pem
      CLIENT_KEY_PATH: /app/certs/client.key
      AWS_FIREHOSE_DELIVERY_STREAM_NAME: PUT-OPS-UHhnn
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_USERS: PUT-OPS-wMGS3
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_COMPANY: PUT-OPS-CfzDC
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION: PUT-OPS-3CjQG
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION_ADDON: PUT-OPS-CMVgb
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_PAYMENTS: PUT-OPS-gO9zU
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_JOBS: PUT-OPS-divA1
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS: PUT-OPS-ZRT55
      AWS_OPENSEARCH_NODE: https://search-urecruits-hr-analytics-dev-5u44ks66ogiff5bmhd36tpqpay.us-east-1.es.amazonaws.com
      AWS_OPENSEARCH_INDEX: hr-analytics
      AWS_OPENSEARCH_USERNAME: uRecruits@admin
      AWS_OPENSEARCH_PASSWORD: "D|*9S+]D6*gz>cD"
      QDRANT_DATABASE_URL: 'https://baf84945-64fc-49fa-a2ea-76aaa18a7cdd.us-east4-0.gcp.cloud.qdrant.io:6333'
      QDRANT_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.mi3m3jbDElbTB6AG1KuVEXKLzDBLmYCG3I9dPDIb4OQ'
      JOBTARGET_CLIENT_ID: '77e211406e6ed4d50d02826d501cfd39'
      JOBTARGET_CLIENT_SECRET: '5808c1e969f8c3b36418b45639df88c8e327088e208b3e16'
      JOBTARGET_API: 'https://uat-partner-api.jobtarget.com/api'
      DOCKERHUB_USER: 'parvfullstack'
      DOCKERHUB_PASS: 'Bhavin1234'
      AUTH0_CANDIDATE_ROLE: 'rol_knAZ2LRaIxsg7Uth'
      AUTH0_HR_RECRUITER_ROLE: 'rol_XSvI7wOambKTG5L2'
      AUTH0_COMPANY_ADMIN_ROLE: 'rol_CbPzwN0RzIzHHNhR'
      AUTH0_COMPANY_OWNER_ROLE: 'rol_6gYKbMHVIsdQovsZ'yarn run start:all
  qa:
    image:
      build:
        dockerfile: dockerfiles/Dockerfile.assessment
        context: .
      port: 3000
    http:
      alias: 'wtt-staging.urecruits.com'yarn run start:all
    cpu: 256
    memory: 512
    count: 1
    exec: true
    secrets:
      SENDGRID_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/SENDGRID_TOKEN
      DB_PASS:
        secretsmanager: 'urecruits-qa-assessment-AuroraPostgres:password::'
      DB_HOST:
        secretsmanager: 'urecruits-qa-assessment-AuroraPostgres:host::'
      DB1_PASS:
        secretsmanager: 'urecruits-qa-assessment-RDSMySQL:password::'
      DB1_HOST:
        secretsmanager: 'urecruits-qa-assessment-RDSMySQL:host::'
      JUDGE0_AUTH_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/JUDGE0_AUTH_TOKEN
      CONVERGENCE_PRIVATE_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/CONVERGENCE_PRIVATE_KEY
      HELLOSIGN_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_KEY
      HELLOSIGN_CLIENT_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_CLIENT_ID
      # OPEN_AI_SECRET_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/OPEN_AI_SECRET_KEY
      TEMPORAL_SERVER: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TEMPORAL_SERVER
    variables:
      DB_NAME: postgres
      DB_USERNAME: postgres
      POSTGRES_PORT: 5432
      REDIS_URL: redis://urecruits-qa-redis.6bseuu.0001.use1.cache.amazonaws.com:6379
      EMAIL_SENDER: <EMAIL>
      FRONT_URL: https://urecruits.com
      JUDGE0_URL: https://judge0-ce.p.rapidapi.com
      JUDGE0_HOST: judge0-ce.p.rapidapi.com
      AUTH0_AUDIENCE: https://api-authz.urecruits.com/
      AUTH0_DOMAIN: https://auth.urecruits.com
      AUTH0_CLIENT_SECRET_MTM: ****************************************************************
      AUTH0_CLIENT_ID_MTM: JvrRE8piU5dTKIzwFgcWpK0TRuMnqUEZ
      AUTH0_DOMAIN_MTM: https://dev-9zt22me9.us.auth0.com
      MICROSERVICES_PRA_URL: https://recruitment-micro.urecruits.com
      HELLOSIGN_TEST_MODE: 1
      ASSESSMENT_API_URI : https://wtt-dev.urecruits.com
      WEB_APP_URI : https://app.urecruits.com
      RECRUITMENT_API_URI : https://recruitment-micro.urecruits.com
      OPEN_AI_MODEL: gpt-4o
      OPEN_AI_SECRET_KEY: ***************************************************
      TEMPORAL_NAMESPACE: recruitment-prod.lrnb6
      CA_PATH: /app/certs/ca.pem
      CLIENT_CA_PATH: /app/certs/client.pem
      CLIENT_KEY_PATH: /app/certs/client.key
      AWS_FIREHOSE_DELIVERY_STREAM_NAME: PUT-OPS-UHhnn
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_USERS: PUT-OPS-wMGS3
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_COMPANY: PUT-OPS-CfzDC
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION: PUT-OPS-3CjQG
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION_ADDON: PUT-OPS-CMVgb
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_PAYMENTS: PUT-OPS-gO9zU
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_JOBS: PUT-OPS-divA1
      AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS: PUT-OPS-ZRT55
      AWS_OPENSEARCH_NODE: https://search-urecruits-hr-analytics-dev-5u44ks66ogiff5bmhd36tpqpay.us-east-1.es.amazonaws.com
      AWS_OPENSEARCH_INDEX: hr-analytics
      AWS_OPENSEARCH_USERNAME: uRecruits@admin
      AWS_OPENSEARCH_PASSWORD: "D|*9S+]D6*gz>cD"
      QDRANT_DATABASE_URL: 'https://baf84945-64fc-49fa-a2ea-76aaa18a7cdd.us-east4-0.gcp.cloud.qdrant.io:6333'
      QDRANT_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.mi3m3jbDElbTB6AG1KuVEXKLzDBLmYCG3I9dPDIb4OQ'
      JOBTARGET_CLIENT_ID: '77e211406e6ed4d50d02826d501cfd39'
      JOBTARGET_CLIENT_SECRET: '5808c1e969f8c3b36418b45639df88c8e327088e208b3e16'
      JOBTARGET_API: 'https://uat-partner-api.jobtarget.com/api'

