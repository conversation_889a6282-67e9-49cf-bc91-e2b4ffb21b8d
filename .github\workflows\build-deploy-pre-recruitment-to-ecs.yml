name: Deploy Pre-recruitment API to Amazon ECS

on:
  push:
    branches:
      - development
    paths:
      - 'apps/pre-recruitment-api/**'
      - 'dockerfiles/Dockerfile.pre-recruitment'
      - 'libs/**'
      - 'tools/**'
      - '*.js'
      - '*.ts'
      - '*.json'
      - '*.env'
      - '*.yml'
      - '*.yaml'

env:
  AWS_REGION: us-east-1 
  DEV_PRE_RECRUITMENT_REPO: development/urecruits-pre-recruitment

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    name: Build & Deploy Pre-recruitment API
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Build Pre-recruitment Docker Image
        run: |
          docker build -t pre-recruitment-api:latest -f dockerfiles/Dockerfile.pre-recruitment .
      
      - name: Push Pre-recruitment Docker Image to ECR
        env:
          AWS_ACCOUNT_ID: ************
          AWS_REGION: us-east-1
        run: |
          aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
          GIT_COMMIT=${GITHUB_SHA::7}                        
          RAND_NUM=$(shuf -i 1000000-9999999 -n 1)            
          IMAGE_TAG="$GIT_COMMIT-$RAND_NUM"
          IMAGE=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$DEV_PRE_RECRUITMENT_REPO:$IMAGE_TAG
          echo "Tagging image as $IMAGE"
          docker tag pre-recruitment-api:latest $IMAGE
          echo "Pushing image $IMAGE"
          docker push $IMAGE
          echo "Image pushed to ECR $IMAGE"
