# The manifest for the "pre-recruitment" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: pre-recruitment
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  # Requests to this path will be forwarded to your service.
  # To match all requests you can use the "/" path.
  path: '/'
  alias: 'recruitment-micro.urecruits.com'
  # You can specify a custom health check path. The default is "/".
  healthcheck:
    path: '/api/'
    success_codes: '200-399'
    healthy_threshold: 5
    unhealthy_threshold: 3
    interval: 30s
    timeout: 10s
    grace_period: 180s

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build:
    dockerfile: dockerfiles/Dockerfile.pre-recruitment
    context: .
  # Port exposed through your container to route traffic to it.
  port: 3001

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
exec: true     # Enable running commands in your container.

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
  REDIS_URL: redis://urecruits-dev-redis.6bseuu.0001.use1.cache.amazonaws.com:6379
  AUTH0_AUDIENCE: https://api-authz.urecruits.com/
  AUTH0_DOMAIN: https://auth.urecruits.com
  AUTH0_DOMAIN_MTM: https://dev-9zt22me9.us.auth0.com
  AUTH0_CLIENT_SECRET_MTM: ****************************************************************
  AUTH0_CLIENT_ID_MTM: JvrRE8piU5dTKIzwFgcWpK0TRuMnqUEZ
  EMAIL_SENDER: <EMAIL>
  FRONT_URL: https://urecruits.com
  GMAIL_AUTH_CLIENT_ID: *************-q26fevtsnvgipp6u9murtsnlko3d6mlq.apps.googleusercontent.com
  GCAL_AUTH_CLIENT_ID: *************-q26fevtsnvgipp6u9murtsnlko3d6mlq.apps.googleusercontent.com
  OUTLOOK_CLIENT_ID: 470682b6-c18c-4115-8569-41888502bb8f
  MCAL_CLIENT_ID: 470682b6-c18c-4115-8569-41888502bb8f
  WEB_APPS_URL: https://app.urecruits.com
  MICROSERVICES_PRA_URL: https://recruitment-micro.urecruits.com
  HELLOSIGN_TEST_MODE: 1
  OPEN_AI_MODEL: gpt-4o
  # TODO: move to secrets
  CRYPTO_PASS: KDLdlfj3fdgdfbKHQzv
  GOOGLE_SERVICE_ACCOUNT_CLIENT_EMAIL: <EMAIL>
  ASSESSMENT_API_URI : https://wtt-dev.urecruits.com
  WEB_APP_URI : https://app.urecruits.com
  RECRUITMENT_API_URI : https://recruitment-micro.urecruits.com
  UNIVERSAL_PASSWORD : MAeDkTiDbAHPYyRu
  UNIVERSAL_API_URL : https://universalapi.universalbackground.com/api/v1/BackgroundCheckInvitation
  FRESHSALES_CONTACT_UPSERT_API : https://urecruits.myfreshworks.com/crm/sales/api/contacts/upsert
  FRESHSALES_NOTES_API : https://urecruits.myfreshworks.com/crm/sales/api/notes
  FRESHSALES_SEARCH_CONTACT_API : https://urecruits.myfreshworks.com/crm/sales/api/filtered_search/contact
  FRESHSALES_AUTH_TOKEN : kphcsbmzzvoQqP6fiIQWqA
  TEMPORAL_NAMESPACE: recruitment-prod.lrnb6
  CLIENT_CA_PATH: /app/certs/client.pem
  CLIENT_KEY_PATH: /app/certs/client.key
  AWS_FIREHOSE_DELIVERY_STREAM_NAME: PUT-OPS-UHhnn
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_USERS: PUT-OPS-wMGS3
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_COMPANY: PUT-OPS-CfzDC
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION: PUT-OPS-3CjQG
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION_ADDON: PUT-OPS-CMVgb
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_PAYMENTS: PUT-OPS-gO9zU
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_JOBS: PUT-OPS-divA1
  AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS: PUT-OPS-ZRT55
  AWS_OPENSEARCH_NODE: https://search-urecruits-hr-analytics-dev-5u44ks66ogiff5bmhd36tpqpay.us-east-1.es.amazonaws.com
  AWS_OPENSEARCH_INDEX: hr-analytics
  AWS_OPENSEARCH_USERNAME: uRecruits@admin
  AWS_OPENSEARCH_PASSWORD: "D|*9S+]D6*gz>cD"
  QDRANT_DATABASE_URL: 'https://baf84945-64fc-49fa-a2ea-76aaa18a7cdd.us-east4-0.gcp.cloud.qdrant.io:6333'
  QDRANT_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.mi3m3jbDElbTB6AG1KuVEXKLzDBLmYCG3I9dPDIb4OQ'
  JOBTARGET_CLIENT_ID: '77e211406e6ed4d50d02826d501cfd39'
  JOBTARGET_CLIENT_SECRET: '5808c1e969f8c3b36418b45639df88c8e327088e208b3e16'
  JOBTARGET_API: 'https://uat-partner-api.jobtarget.com/api'
  DOCKERHUB_USER: 'parvfullstack'
  DOCKERHUB_PASS: 'Bhavin1234'
  AUTH0_CANDIDATE_ROLE: 'rol_knAZ2LRaIxsg7Uth'
  AUTH0_HR_RECRUITER_ROLE: 'rol_XSvI7wOambKTG5L2'
  AUTH0_COMPANY_ADMIN_ROLE: 'rol_CbPzwN0RzIzHHNhR'
  AUTH0_COMPANY_OWNER_ROLE: 'rol_6gYKbMHVIsdQovsZ'


secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
  PRERECRUITMENTMICRO_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/PRERECRUITMENTMICRO_SECRET
  AWS_ACCESS_KEY_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/DEV_AWS_ACCESS_KEY_ID
  AWS_SECRET_ACCESS_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/DEV_AWS_ACCESS_SECRET
  STRIPE_SECRET_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/STRIPE_SECRET_KEY
  SENDGRID_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/SENDGRID_TOKEN
  GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY
  HELLOSIGN_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_KEY
  HELLOSIGN_CLIENT_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/HELLOSIGN_CLIENT_ID
  OPEN_AI_SECRET_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/OPEN_AI_SECRET_KEY
  GMAIL_AUTH_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/GMAIL_AUTH_CLIENT_SECRET
  GCAL_AUTH_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/GCAL_AUTH_CLIENT_SECRET
  OUTLOOK_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/OUTLOOK_CLIENT_SECRET
  MCAL_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/MCAL_CLIENT_SECRET
  TEMPORAL_SERVER: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TEMPORAL_SERVER
  TWILIO_ACCOUNT_SID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TWILIO_ACCOUNT_SID
  TWILIO_AUTH_TOKEN: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TWILIO_AUTH_TOKEN
  TWILIO_VIDEO_API_KEY: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TWILIO_VIDEO_API_KEY
  TWILIO_VIDEO_API_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TWILIO_VIDEO_API_SECRET
  TWILIO_CHAT_SERVICE_SID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/TWILIO_CHAT_SERVICE_SID

# You can override any of the values defined above by environment.
#environments:
#  test:
#    count: 2               # Number of tasks to run for the "test" environment.
#    deployment:            # The deployment strategy for the "test" environment.
#       rolling: 'recreate' # Stops existing tasks before new ones are started for faster deployments.
